using AwhGameServer.Infrastructure.Extensions;
using AwhGameServer.WebApi.Extensions;

var builder = WebApplication.CreateBuilder(args);

builder.Configuration.AddEnvironmentVariablesAliasesFromJson();

builder.Services                                    // Регистрация сервисов всех слоев приложения в контейнере зависимостей
    .AddApplication()                                  // Сервисы уровня приложения (use cases, handlers)
    .AddInfrastructure(builder.Configuration)          // Сервисы инфраструктуры (БД, внешние API)
    .AddWebApi(builder.Configuration, builder.Host);   // Сервисы веб-API (контроллеры, валидация)

var app = builder.Build();

app.UseWebApi();   // Настройка конвейера обработки HTTP-запросов

app.Run();

public partial class Program;   // Позволяет получить доступ к типу Program из тестовых проектов
