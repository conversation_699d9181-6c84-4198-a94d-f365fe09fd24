# AwhGameServer

## Запуск в dokcer compose

1) Создать файл `.env` в корневом каталоге репозитория (рядом с `compose.yaml`)
2) Вставить в файл следующий текст:

```env
APP_PORT_MAPPING=127.0.0.1:8080
MONGO_DB_PORT_MAPPING=127.0.0.1:27017
SEQ_PORT_MAPPING=127.0.0.1:8090
HMAC_TOKEN_HASHER_PEPPER_BASE64=<СЛУЧАЙНАЯ СТРОКА ДЛИННОЙ НЕ МЕНЕЕ ЧЕМ 64 СИМВОЛА>
AUTH_SESSION_ACCESS_TOKEN_SECRET=<СЛУЧАЙНАЯ СТРОКА ДЛИННОЙ НЕ МЕНЕЕ ЧЕМ 64 СИМВОЛА>
```

3) В файле заменить значения в треугольных скобках `VAR=<ИНСТРУКЦИЯ ПО ЗАПОЛНЕНИЮ>` -> `VAR=correct_value`
4) Выполнить команду `docker compose up -d --build`
5) Сервер будет доступен по адресу  `127.0.0.1:8080`

## Запуск через drone

* Необходимо создать следующие секреты в drone:
    * `PROJECTS_HOME` - путь к каталогу с проектами
    * `APP_PORT_MAPPING` - порт на котором будет доступен сервер
    * `MONGO_DB_PORT_MAPPING` - порт на котором будет доступен MongoDB на сервере (для подключения по ssh через Compass)
    * `SEQ_PORT_MAPPING` - порт на котором будет доступен Seq
    * `HMAC_TOKEN_HASHER_PEPPER_BASE64` - pepper для HMAC-хешера токенов
    * `AUTH_SESSION_ACCESS_TOKEN_SECRET` - секретный ключ для подписи токена доступа
